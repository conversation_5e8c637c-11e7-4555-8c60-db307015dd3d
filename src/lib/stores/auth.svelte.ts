/**
 * Authentication state interface for the reactive class
 */

import { authClient } from "$utils/auth-client";
import type { Session, User } from "@prisma/client";
import type { Atom, BetterFetchError } from "better-auth/svelte";

type AuthSession = Awaited<ReturnType<typeof authClient.useSession>>;

interface AuthState {
	authSession: AuthSession | null;
	session: Session | null;
	user: User | null;
	isAuthenticated: boolean;
	isLoading: boolean;
	error: BetterFetchError | null;
	loginWithGithub: () => Promise<void>;
}

class AuthStateClass implements AuthState {
	// Reactive state using svelte5 runes
	authSession = $state<AuthSession | null>(null);
	session = $state<Session | null>(null);
	user = $state<User | null>(null);
	isAuthenticated = $state<boolean>(false);
	isLoading = $state<boolean>(true);
	error = $state<BetterFetchError | null>(null);

	constructor() {
		this.initializeAuth();
	}

	private async initializeAuth(): Promise<void> {
		try {
			this.isLoading = true;
			this.authSession = await authClient.useSession();

			// Update other state based on authSession
			if (this.authSession) {
				this.isAuthenticated = true
			}
		}
	};
}
